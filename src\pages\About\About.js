import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './About.css';

const About = () => {
  const aboutRef = useRef(null);
  const skillsRef = useRef(null);

  useEffect(() => {
    // Animate about content on scroll
    gsap.fromTo('.about-content > *',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        stagger: 0.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: aboutRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
        }
      }
    );

    // Animate skills
    gsap.fromTo('.skill-item',
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: skillsRef.current,
          start: 'top 80%',
        }
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const skills = [
    'JavaScript', 'React', 'Node.js', 'TypeScript',
    'GSAP', 'Three.js', 'CSS/SCSS', 'MongoDB',
    'Express', 'Git', 'Figma', 'Photoshop'
  ];

  return (
    <div className="about">
      <section className="about-hero section" ref={aboutRef}>
        <div className="container">
          <div className="about-content">
            <h1>About Me</h1>
            <div className="about-text">
              <p>
                I'm a passionate full-stack developer with a keen eye for design and
                a love for creating seamless user experiences. With over 3 years of
                experience in web development, I specialize in modern JavaScript
                frameworks and creative coding.
              </p>
              <p>
                My journey began with a curiosity about how websites work, which
                quickly evolved into a deep passion for crafting digital experiences
                that are both beautiful and functional. I believe that great design
                and clean code go hand in hand.
              </p>
              <p>
                When I'm not coding, you can find me exploring new technologies,
                contributing to open-source projects, or experimenting with creative
                coding and generative art.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="skills section" ref={skillsRef}>
        <div className="container">
          <h2>Skills & Technologies</h2>
          <div className="skills-grid">
            {skills.map((skill, index) => (
              <div key={skill} className="skill-item" style={{ '--delay': `${index * 0.1}s` }}>
                <span>{skill}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="experience section">
        <div className="container">
          <h2>Experience</h2>
          <div className="timeline">
            <div className="timeline-item">
              <div className="timeline-date">2023 - Present</div>
              <div className="timeline-content">
                <h3>Senior Frontend Developer</h3>
                <p>Leading frontend development for modern web applications using React, TypeScript, and GSAP.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-date">2021 - 2023</div>
              <div className="timeline-content">
                <h3>Full Stack Developer</h3>
                <p>Developed and maintained full-stack applications using MERN stack and modern development practices.</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-date">2020 - 2021</div>
              <div className="timeline-content">
                <h3>Junior Developer</h3>
                <p>Started my professional journey, focusing on frontend development and learning industry best practices.</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
