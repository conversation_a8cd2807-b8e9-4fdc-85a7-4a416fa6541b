import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './Projects.css';

const Projects = () => {
  const projectsRef = useRef(null);

  useEffect(() => {
    // Animate project cards on scroll
    gsap.fromTo('.project-card',
      { opacity: 0, y: 100 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        stagger: 0.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: projectsRef.current,
          start: 'top 80%',
        }
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A modern e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    },
    {
      id: 2,
      title: 'Creative Portfolio',
      description: 'An interactive portfolio website with smooth animations and 3D elements. Built using React, GSAP, and Three.js for immersive user experience.',
      technologies: ['React', 'GSAP', 'Three.js', 'CSS3'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    },
    {
      id: 3,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
      technologies: ['React', 'Socket.io', 'Express', 'PostgreSQL'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    },
    {
      id: 4,
      title: 'Weather Dashboard',
      description: 'A responsive weather dashboard with location-based forecasts, interactive maps, and data visualization using Chart.js.',
      technologies: ['JavaScript', 'Chart.js', 'OpenWeather API', 'CSS3'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    },
    {
      id: 5,
      title: 'Social Media App',
      description: 'A full-stack social media application with user profiles, post sharing, real-time messaging, and image upload functionality.',
      technologies: ['React', 'Firebase', 'Node.js', 'Cloudinary'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    },
    {
      id: 6,
      title: 'Crypto Tracker',
      description: 'A cryptocurrency tracking application with real-time price updates, portfolio management, and market analysis tools.',
      technologies: ['React', 'Redux', 'CoinGecko API', 'Chart.js'],
      image: '/api/placeholder/400/300',
      github: '#',
      live: '#'
    }
  ];

  return (
    <div className="projects">
      <section className="projects-hero section">
        <div className="container">
          <h1>My Projects</h1>
          <p>A collection of work that showcases my skills and passion for development</p>
        </div>
      </section>

      <section className="projects-grid section" ref={projectsRef}>
        <div className="container">
          <div className="grid">
            {projects.map((project) => (
              <div key={project.id} className="project-card">
                <div className="project-image">
                  <img src={project.image} alt={project.title} />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href={project.github} className="project-link">
                        <span>GitHub</span>
                      </a>
                      <a href={project.live} className="project-link">
                        <span>Live Demo</span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3>{project.title}</h3>
                  <p>{project.description}</p>
                  <div className="project-technologies">
                    {project.technologies.map((tech) => (
                      <span key={tech} className="tech-tag">{tech}</span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Projects;
