.about {
  background: #0a0a0a;
}

.about-hero {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-content h1 {
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-text {
  text-align: left;
}

.about-text p {
  margin-bottom: 2rem;
  color: #cccccc;
  font-size: 1.1rem;
  line-height: 1.8;
}

.skills {
  background: #0f0f0f;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.skills h2 {
  text-align: center;
  margin-bottom: 3rem;
  color: #ffffff;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.skill-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.skill-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-5px);
}

.skill-item span {
  font-weight: 600;
  color: #ffffff;
}

.experience {
  background: #0a0a0a;
}

.experience h2 {
  text-align: center;
  margin-bottom: 4rem;
  color: #ffffff;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  margin-bottom: 3rem;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 1rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-date {
  flex: 0 0 45%;
  text-align: right;
  padding-right: 2rem;
  color: #667eea;
  font-weight: 600;
}

.timeline-item:nth-child(even) .timeline-date {
  text-align: left;
  padding-right: 0;
  padding-left: 2rem;
}

.timeline-content {
  flex: 0 0 45%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.timeline-content h3 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.timeline-content p {
  color: #cccccc;
  line-height: 1.6;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about-content {
    text-align: left;
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }

  .timeline::before {
    left: 1rem;
  }

  .timeline-item {
    flex-direction: column !important;
    padding-left: 3rem;
  }

  .timeline-item::before {
    left: 1rem;
    transform: translateX(-50%);
  }

  .timeline-date {
    text-align: left !important;
    padding: 0 0 1rem 0 !important;
    font-size: 0.9rem;
  }

  .timeline-content {
    flex: 1;
  }
}
