.home {
  position: relative;
}

.hero {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  opacity: 0.6;
}

.floating-shape {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-shape.triangle {
  width: 0;
  height: 0;
  background: none;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 52px solid #667eea;
  border-radius: 0;
}

.floating-shape.square {
  border-radius: 12px;
  transform: rotate(45deg);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.hero-bg {
  position: absolute;
  top: -20%;
  left: -20%;
  width: 140%;
  height: 140%;
  background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.hero-content:hover .hero-title {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.hero-title {
  margin-bottom: 2rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  color: #cccccc;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.cta-button.secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #888;
  font-size: 0.9rem;
}

.scroll-line {
  width: 1px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, #667eea, transparent);
  animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

.intro {
  background: #0f0f0f;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.intro-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.intro-content h2 {
  margin-bottom: 2rem;
  color: #ffffff;
}

.intro-content p {
  color: #cccccc;
  font-size: 1.2rem;
  line-height: 1.8;
}

/* Gallery Section */
.gallery-section {
  background: #0f0f0f;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  perspective: 1200px;
  perspective-origin: center center;
}

.gallery-header {
  text-align: center;
  margin-bottom: 4rem;
}

.gallery-header h2 {
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gallery-header p {
  color: #cccccc;
  font-size: 1.1rem;
}

.gallery-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.gallery-item {
  position: absolute;
  width: 280px;
  height: 350px;
  left: 50%;
  top: 50%;
  margin-left: -140px;
  margin-top: -175px;
  transform-style: preserve-3d;
  transition: all 0.6s ease-in-out;
  cursor: pointer;
}

.gallery-item.active .gallery-item-inner {
  transform: scale(1.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.gallery-item-inner {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
}

.gallery-item:hover .gallery-item-inner {
  border-color: var(--item-color);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.gallery-image {
  width: 100%;
  height: 60%;
  overflow: hidden;
  position: relative;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-image img {
  transform: scale(1.1);
}

.gallery-content {
  padding: 1.5rem;
  text-align: center;
  height: 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.gallery-content h3 {
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

.gallery-content p {
  color: #cccccc;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Gallery Controls */
.gallery-controls {
  position: absolute;
  top: 50%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  pointer-events: none;
  z-index: 10;
}

.gallery-btn {
  width: 50px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: all;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.gallery-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.gallery-btn span {
  line-height: 1;
}

/* Gallery Dots */
.gallery-dots {
  position: absolute;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
}

.gallery-dot {
  width: 12px;
  height: 12px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-dot.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(1.2);
}

.gallery-dot:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 280px;
  }

  .scroll-indicator {
    display: none;
  }

  .floating-elements {
    display: none;
  }

  .gallery-container {
    height: 400px;
    padding: 0 1rem;
  }

  .gallery-item {
    width: 250px;
    height: 300px;
    margin-left: -125px;
    margin-top: -150px;
  }

  .gallery-controls {
    padding: 0 1rem;
  }

  .gallery-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .gallery-dots {
    bottom: -40px;
    gap: 0.5rem;
  }

  .gallery-dot {
    width: 8px;
    height: 8px;
  }

  .gallery-header {
    margin-bottom: 2rem;
  }
}
