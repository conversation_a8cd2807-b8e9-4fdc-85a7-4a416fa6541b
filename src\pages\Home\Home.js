import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './Home.css';

const Home = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline({ delay: 2.5 });

    // Hero animations
    tl.fromTo(titleRef.current,
      { opacity: 0, y: 100 },
      { opacity: 1, y: 0, duration: 1.2, ease: 'power3.out' }
    )
    .fromTo(subtitleRef.current,
      { opacity: 0, y: 50 },
      { opacity: 1, y: 0, duration: 1, ease: 'power3.out' },
      '-=0.8'
    )
    .fromTo(ctaRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' },
      '-=0.6'
    );

    // Parallax effect for hero background
    gsap.to('.hero-bg', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: heroRef.current,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const scrollToProjects = () => {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="home">
      <section className="hero section" ref={heroRef}>
        <div className="hero-bg"></div>
        <div className="container">
          <div className="hero-content">
            <h1 ref={titleRef} className="hero-title">
              Creative
              <span className="text-gradient"> Developer</span>
            </h1>
            <p ref={subtitleRef} className="hero-subtitle">
              I craft digital experiences that blend creativity with functionality.
              Specializing in modern web technologies and interactive design.
            </p>
            <div ref={ctaRef} className="hero-cta">
              <button className="cta-button primary" onClick={scrollToProjects}>
                View My Work
              </button>
              <button className="cta-button secondary">
                Get In Touch
              </button>
            </div>
          </div>
        </div>
        <div className="scroll-indicator">
          <div className="scroll-line"></div>
          <span>Scroll</span>
        </div>
      </section>

      <section className="intro section">
        <div className="container">
          <div className="intro-content">
            <h2>Building Digital Experiences</h2>
            <p>
              With a passion for clean code and beautiful design, I create web applications
              that not only look great but perform exceptionally. Every project is an
              opportunity to push boundaries and explore new possibilities.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
