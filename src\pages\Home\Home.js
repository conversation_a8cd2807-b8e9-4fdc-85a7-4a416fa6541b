import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './Home.css';

const Home = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const galleryRef = useRef(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Gallery data
  const galleryItems = [
    {
      id: 1,
      title: "Web Development",
      description: "Modern responsive websites",
      image: "https://images.unsplash.com/photo-1547658719-da2b51169166?w=400&h=400&fit=crop&crop=center",
      color: "#667eea"
    },
    {
      id: 2,
      title: "Mobile Apps",
      description: "Cross-platform applications",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=400&fit=crop&crop=center",
      color: "#764ba2"
    },
    {
      id: 3,
      title: "UI/UX Design",
      description: "Beautiful user interfaces",
      image: "https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=400&fit=crop&crop=center",
      color: "#f093fb"
    },
    {
      id: 4,
      title: "E-Commerce",
      description: "Online shopping platforms",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop&crop=center",
      color: "#f5576c"
    },
    {
      id: 5,
      title: "Animation",
      description: "Interactive experiences",
      image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop&crop=center",
      color: "#4facfe"
    },
    {
      id: 6,
      title: "Branding",
      description: "Visual identity design",
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=400&fit=crop&crop=center",
      color: "#43e97b"
    }
  ];

  useEffect(() => {
    const tl = gsap.timeline({ delay: 2.5 });

    // Hero animations
    tl.fromTo(titleRef.current,
      { opacity: 0, y: 100 },
      { opacity: 1, y: 0, duration: 1.2, ease: 'power3.out' }
    )
    .fromTo(subtitleRef.current,
      { opacity: 0, y: 50 },
      { opacity: 1, y: 0, duration: 1, ease: 'power3.out' },
      '-=0.8'
    )
    .fromTo(ctaRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' },
      '-=0.6'
    );

    // Parallax effect for hero background
    gsap.to('.hero-bg', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: heroRef.current,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true
      }
    });

    // Floating elements animation
    gsap.to('.floating-element', {
      y: -20,
      duration: 2,
      ease: 'power2.inOut',
      yoyo: true,
      repeat: -1,
      stagger: 0.3
    });

    // Mouse move parallax effect
    const handleMouseMove = (e) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      const xPos = (clientX / innerWidth - 0.5) * 2;
      const yPos = (clientY / innerHeight - 0.5) * 2;

      gsap.to('.floating-element', {
        x: xPos * 30,
        y: yPos * 30,
        duration: 1,
        ease: 'power2.out',
        stagger: 0.1
      });

      gsap.to('.hero-bg', {
        x: xPos * 20,
        y: yPos * 20,
        duration: 1,
        ease: 'power2.out'
      });
    };

    const heroSection = heroRef.current;
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
    }

    // Gallery carousel animation
    const galleryAnimation = () => {
      const items = document.querySelectorAll('.gallery-item');
      const totalItems = items.length;
      const angleStep = 360 / totalItems;
      const radius = window.innerWidth < 768 ? 150 : 250;

      items.forEach((item, index) => {
        const angle = (index * angleStep) + (currentSlide * -angleStep);
        const radian = (angle * Math.PI) / 180;
        const x = Math.cos(radian) * radius;
        const z = Math.sin(radian) * radius;
        const scale = z > 0 ? 0.8 : 1;
        const opacity = z > 0 ? 0.6 : 1;

        gsap.to(item, {
          x: x,
          z: z,
          rotationY: angle,
          scale: scale,
          opacity: opacity,
          duration: 0.6,
          ease: 'power2.out',
          transformOrigin: 'center center'
        });
      });
    };

    // Initial gallery setup
    setTimeout(() => {
      galleryAnimation();
    }, 100);

    // Auto-rotate gallery
    const autoRotate = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % galleryItems.length);
    }, 4000);

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      clearInterval(autoRotate);
      if (heroSection) {
        heroSection.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, [currentSlide]);

  const scrollToProjects = () => {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % galleryItems.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + galleryItems.length) % galleryItems.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  return (
    <div className="home">
      <section className="hero section" ref={heroRef}>
        <div className="hero-bg"></div>

        {/* Floating Elements */}
        <div className="floating-elements">
          <div className="floating-element" style={{top: '20%', left: '10%'}}>
            <div className="floating-shape circle"></div>
          </div>
          <div className="floating-element" style={{top: '60%', right: '15%'}}>
            <div className="floating-shape triangle"></div>
          </div>
          <div className="floating-element" style={{top: '40%', left: '80%'}}>
            <div className="floating-shape square"></div>
          </div>
        </div>

        <div className="container">
          <div className="hero-content">
            <h1 ref={titleRef} className="hero-title">
              Creative
              <span className="text-gradient"> Developer</span>
            </h1>
            <p ref={subtitleRef} className="hero-subtitle">
              I craft digital experiences that blend creativity with functionality.
              Specializing in modern web technologies and interactive design.
            </p>
            <div ref={ctaRef} className="hero-cta">
              <button className="cta-button primary" onClick={scrollToProjects}>
                View My Work
              </button>
              <button className="cta-button secondary">
                Get In Touch
              </button>
            </div>
          </div>
        </div>
        <div className="scroll-indicator">
          <div className="scroll-line"></div>
          <span>Scroll</span>
        </div>
      </section>

      {/* Interactive Gallery Section */}
      <section className="gallery-section section" ref={galleryRef}>
        <div className="container">
          <div className="gallery-header">
            <h2>What I Do</h2>
            <p>Explore my areas of expertise through this interactive showcase</p>
          </div>

          <div className="gallery-container">
            <div className="gallery-carousel">
              {galleryItems.map((item, index) => (
                <div
                  key={item.id}
                  className={`gallery-item ${index === currentSlide ? 'active' : ''}`}
                  style={{'--item-color': item.color}}
                >
                  <div className="gallery-item-inner">
                    <div className="gallery-image">
                      <img src={item.image} alt={item.title} />
                    </div>
                    <div className="gallery-content">
                      <h3>{item.title}</h3>
                      <p>{item.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="gallery-controls">
              <button className="gallery-btn prev" onClick={prevSlide}>
                <span>‹</span>
              </button>
              <button className="gallery-btn next" onClick={nextSlide}>
                <span>›</span>
              </button>
            </div>

            <div className="gallery-dots">
              {galleryItems.map((_, index) => (
                <button
                  key={index}
                  className={`gallery-dot ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="intro section">
        <div className="container">
          <div className="intro-content">
            <h2>Building Digital Experiences</h2>
            <p>
              With a passion for clean code and beautiful design, I create web applications
              that not only look great but perform exceptionally. Every project is an
              opportunity to push boundaries and explore new possibilities.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
