/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: auto; /* Let Lenis handle smooth scrolling */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', '<PERSON>xygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0a0a;
  color: #ffffff;
  overflow-x: hidden;
}

.App {
  min-height: 100vh;
  position: relative;
}

/* Main content wrapper */
main {
  position: relative;
  z-index: 1;
}

/* Section base styles */
.section {
  min-height: 100vh;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 700;
}

h2 {
  font-size: clamp(2rem, 5vw, 4rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
}

p {
  line-height: 1.6;
  font-size: clamp(1rem, 2vw, 1.2rem);
}

/* Utility classes */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Responsive design */
@media (max-width: 768px) {
  .section {
    padding: 0 1rem;
  }

  .container {
    padding: 0 1rem;
  }
}
