.contact {
  background: #0a0a0a;
}

.contact-hero {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.contact-content h1 {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-content > p {
  color: #cccccc;
  font-size: 1.2rem;
  margin-bottom: 4rem;
  line-height: 1.6;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.contact-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-5px);
}

.contact-item h3 {
  color: #667eea;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.contact-item p {
  color: #ffffff;
  font-weight: 600;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.social-link {
  color: #cccccc;
  text-decoration: none;
  font-weight: 600;
  padding: 0.8rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  transition: all 0.3s ease;
}

.social-link:hover {
  color: #667eea;
  border-color: #667eea;
  transform: translateY(-2px);
}

.contact-form-section {
  background: #0f0f0f;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.contact-form h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #ffffff;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cccccc;
  font-weight: 600;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  width: 100%;
  padding: 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .contact-info {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-item {
    padding: 1.5rem 1rem;
  }

  .social-links {
    gap: 1rem;
  }

  .social-link {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .contact-form {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
}
