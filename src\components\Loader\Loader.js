import React, { useEffect, useState } from 'react';
import { gsap } from 'gsap';
import './Loader.css';

const Loader = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const tl = gsap.timeline();

    // Animate loader text
    tl.fromTo('.loader-text', 
      { opacity: 0, y: 50 },
      { opacity: 1, y: 0, duration: 1, ease: 'power3.out' }
    )
    .to('.loader-progress', {
      width: '100%',
      duration: 1.5,
      ease: 'power2.inOut'
    })
    .to('.loader-text', {
      opacity: 0,
      y: -50,
      duration: 0.5,
      ease: 'power3.in'
    }, '-=0.5')
    .to('.loader', {
      y: '-100%',
      duration: 1,
      ease: 'power3.inOut',
      onComplete: () => {
        setIsLoading(false);
      }
    });

    return () => {
      tl.kill();
    };
  }, []);

  if (!isLoading) return null;

  return (
    <div className="loader">
      <div className="loader-content">
        <div className="loader-text">
          <h1>Welcome</h1>
          <p>Loading Portfolio...</p>
        </div>
        <div className="loader-progress-container">
          <div className="loader-progress"></div>
        </div>
      </div>
    </div>
  );
};

export default Loader;
